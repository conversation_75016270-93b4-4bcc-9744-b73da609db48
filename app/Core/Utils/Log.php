<?php


namespace App\Core\Utils;


use App\Core\Monolog\Handler\CrontabLoggerHandler;
use Hyperf\Context\Context;
use Hyperf\Utils\ApplicationContext;

class Log
{
    protected static $coLogArr=[];
    public static function get(string $name = 'app', string $group = 'default'): \Psr\Log\LoggerInterface
    {
        $crontabLogsName=Context::get(CrontabLoggerHandler::CrontabTaskName,null);
        if ($crontabLogsName) {
            $coId=\Hyperf\Utils\Coroutine::id();
            if(!isset(self::$coLogArr[$coId])){
                self::$coLogArr[$coId]=make(\Hyperf\Logger\LoggerFactory::class)->get('crontab','crontab');
            }
            $log=self::$coLogArr[$coId];
            //$log = ApplicationContext::getContainer()->get(\Hyperf\Logger\LoggerFactory::class)->get('crontab','crontab');
            //$log = ApplicationContext::getContainer()->get(\Hyperf\Logger\LoggerFactory::class)->get('crontab','crontab');
        }else{
            $log = ApplicationContext::getContainer()->get(\Hyperf\Logger\LoggerFactory::class)->get($name, $group);
        }
        $log->setTimezone(new \DateTimeZone('Asia/Shanghai'));
        return $log;
    }

    public static function unsetLogs($coId=null){
        if($coId===null){
            $coId=\Hyperf\Utils\Coroutine::id();
        }
        unset(self::$coLogArr[$coId]);
    }
}