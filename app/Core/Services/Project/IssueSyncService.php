<?php

declare(strict_types=1);

namespace App\Core\Services\Project;

use App\Constants\StatusCode;
use App\Core\Services\BaseService;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\Redmine\IssueCopyRelationsModel;
use App\Model\Redmine\IssuesExtModel;
use App\Model\Redmine\IssueSyncFieldsModel;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\JournalDetailsModel;
use App\Model\Redmine\JournalsModel;
use App\Model\Redmine\CustomValuesModel;
use App\Model\Redmine\UserModel;
use Carbon\Carbon;
use Hyperf\Database\Model\Collection;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Utils\ApplicationContext;
use Hyperf\Utils\Coroutine;

/**
 * 问题同步服务类
 *
 */
class IssueSyncService extends BaseService
{
    /**
     * @Inject()
     * @var IssueCopyRelationsModel
     */
    protected $relationModel;

    /**
     * @Inject()
     * @var IssueSyncFieldsModel
     */
    protected $syncFieldsModel;

    /**
     * @Inject()
     * @var IssueModel
     */
    protected $issueModel;

    /**
     * @Inject()
     * @var JournalsModel
     */
    protected $journalsModel;

    /**
     * @Inject()
     * @var JournalDetailsModel
     */
    protected $journalDetailsModel;



    /**
     * 创建问题同步关系
     *
     * @param int $sourceIssueId 源问题ID
     * @param int $targetIssueId 目标问题ID
     * @param string $syncDirection 同步方向
     * @param array $syncFields 需要同步的字段配置
     * @return IssueCopyRelationsModel
     * @throws AppException
     */
    public function createSyncRelation(
        int $sourceIssueId,
        int $targetIssueId,
        string $syncDirection = IssueCopyRelationsModel::SYNC_BIDIRECTIONAL,
        array $syncFields = []
    ): IssueCopyRelationsModel {
        try {
            // 验证问题是否存在
            $sourceIssue = make(IssueModel::class)->find($sourceIssueId);
            $targetIssue = make(IssueModel::class)->find($targetIssueId);
            
            if (!$sourceIssue) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "源问题不存在");
            }
            
            if (!$targetIssue) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "目标问题不存在");
            }
            
            // 检查是否已存在同步关系
            $existingRelation = $this->relationModel
                ->where('source_issue_id', $sourceIssueId)
                ->where('target_issue_id', $targetIssueId)
                ->first();
                
            if ($existingRelation) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系已存在");
            }
            
            // 获取当前用户信息
            $currentUserId = $this->getCurrentUser();

            // 开启事务
            Db::connection('tchip_redmine')->beginTransaction();

            try {
                // 创建同步关系
                $relation = new IssueCopyRelationsModel();
                $relation->source_issue_id = $sourceIssueId;
                $relation->target_issue_id = $targetIssueId;
                $relation->sync_direction = $syncDirection;
                $relation->is_active = 1;
                $relation->created_by = $currentUserId;
                $relation->updated_by = $currentUserId;
                $relation->created_at = Carbon::now();
                $relation->updated_at = Carbon::now();
                $relation->save();

                // 如果指定了同步字段，创建字段配置
                if (!empty($syncFields)) {
                    $this->createSyncFieldsConfig($relation->id, $syncFields);
                } else {
                    // 使用默认配置
                    $this->createDefaultSyncFieldsConfig($relation->id);
                }


                Db::connection('tchip_redmine')->commit();

                 Log::get()->info('创建同步关系成功', [
                    'relation_id' => $relation->id,
                    'source_issue_id' => $sourceIssueId,
                    'target_issue_id' => $targetIssueId,
                    'sync_direction' => $syncDirection
                ]);

                return $relation;

            } catch (\Exception $e) {
                Db::connection('tchip_redmine')->rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::get('debug')->error('创建同步关系失败', [
                'source_issue_id' => $sourceIssueId,
                'target_issue_id' => $targetIssueId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 更新同步关系配置
     *
     * @param int $relationId 同步关系ID
     * @param array $data 更新数据
     * @return bool
     * @throws AppException
     */
    public function updateSyncRelation(int $relationId, array $data): bool
    {
        try {
            $relation = $this->relationModel->find($relationId);

            if (!$relation) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系不存在");
            }

            $currentUserId = $this->getCurrentUser();

            // 更新同步关系
            if (isset($data['sync_direction'])) {
                $relation->sync_direction = $data['sync_direction'];
            }

            if (isset($data['is_active'])) {
                $relation->is_active = $data['is_active'];
            }

            $relation->updated_by = $currentUserId;
            $relation->updated_at = Carbon::now();
            $relation->save();

            // 更新同步字段配置
            if (isset($data['sync_fields'])) {
                $this->updateSyncFieldsConfig($relationId, $data['sync_fields']);
            }

             Log::get()->info('更新同步关系成功', [
                'relation_id' => $relationId,
                'data' => $data
            ]);

            return true;

        } catch (\Exception $e) {
            Log::get('debug')->error('更新同步关系失败', [
                'relation_id' => $relationId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 删除同步关系
     *
     * @param int $relationId 同步关系ID
     * @return bool
     * @throws AppException
     */
    public function deleteSyncRelation(int $relationId): bool
    {
        try {
            $relation = $this->relationModel->find($relationId);

            if (!$relation) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系不存在");
            }

            Db::connection('tchip_redmine')->beginTransaction();

            try {
                // 删除同步字段配置
                $this->syncFieldsModel->where('relation_id', $relationId)->delete();

                // 软删除同步关系
                $relation->deleted_at = Carbon::now();
                $relation->save();

                Db::connection('tchip_redmine')->commit();

                 Log::get()->info('删除同步关系成功', ['relation_id' => $relationId]);

                return true;

            } catch (\Exception $e) {
                Db::connection('tchip_redmine')->rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::get('debug')->error('删除同步关系失败', [
                'relation_id' => $relationId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 同步问题变更
     *
     * @param int $issueId 发生变更的问题ID
     * @param array $changedFields 变更的字段
     * @param array $newValues 新值
     * @param array $extValues 扩展值
     * @return void
     */
    public function syncIssueChanges(int $issueId, array $changedFields,  array $newValues, array $extValues): void
    {
        try {
            // 查找相关的同步关系
            $relations = $this->findActiveSyncRelations($issueId);

            if ($relations->isEmpty()) {
                return;
            }

            foreach ($relations as $relation) {
                // 判断同步方向
                $isSource = $relation->source_issue_id === $issueId;
                $targetIssueId = $isSource ? $relation->target_issue_id : $relation->source_issue_id;

                // 检查同步方向是否允许
                if ($isSource && !$relation->canSyncFromSource()) {
                    continue;
                }

                if (!$isSource && !$relation->canSyncFromTarget()) {
                    continue;
                }

                // 获取需要同步的字段配置
                $syncFields = $this->getSyncFieldsForRelation($relation->id);

                // 过滤出需要同步的字段
                $fieldsToSync = array_intersect($changedFields, $syncFields);

                if (empty($fieldsToSync)) {
                    continue;
                }

                $userId = $this->getCurrentUser();

                // 异步执行同步操作
                Coroutine::create(function () use ($targetIssueId, $fieldsToSync, $newValues, $relation, $userId, $extValues) {
                    $this->performSync($targetIssueId, $fieldsToSync, $newValues, $relation->id, $userId, $extValues);
                });
            }

        } catch (\Exception $e) {
            Log::get('debug')->error('同步问题变更失败', [
                'issue_id' => $issueId,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function initIssueSyncValues(int $issueId, array $values, array $extValues)
    {
        // BI允许的字段列表，单参传入符合标准的单字段内容修改，多参传入为前端使用上问题此时需要忽略单参传入项的修改
        $allowedFields = [
            'subject', // 标题， 多参传入，修改时传参参数过多需要比对是否存在不同需要处理
            'description', // 描述，多参传入，修改时传参参数过多需要比对是否存在不同需要处理；需要同时处理description_html和description
            'attachment', // 附件，数组，多参传入，修改时传参参数过多需要比对是否存在不同需要处理
            'status_id', // 状态， 单参传入
            'priority_id', // 优先级， 单参传入
            'watchers', // 关注人，单参数组传入
            'assigned', // 处理人，单参数组传入
            'start_date', // 开始时间，单参传入
            'due_date', // 结束时间，单参传入
            'tracker_id', // 事项类型， 单参传入
        ];

        $syncValues = [];


        foreach ($values as $key => $value) {
            // lock_version 处理
            if ($key === 'lock_version') {
                // $syncValues[$key] = $value;
                continue;
            }

            // 判断普通字段是否允许
            if (in_array($key, $allowedFields, true)) {
                // watchers 和 assigned 只保留数字 ID
                if (in_array($key, ['watchers', 'assigned'], true) && is_array($value)) {
                    $syncValues[$key] = array_filter($value, 'is_numeric');
                } else {
                    $syncValues[$key] = $value;
                }
            }
        }

        // 获取旧值
        $oldValues = make(IssueModel::class)
            ->with(['attachment', 'issueAssigned', 'watcher'])
            ->find($issueId)
            ->toArray();
        // 多处理人 assigned 处理
        if (!empty($oldValues['issue_assigned'])) {
            $oldValues['assigned'] = array_column($oldValues['issue_assigned'], 'user_id');
            unset($oldValues['issue_assigned']);
        }
        // 关注人 watchers 处理
        if (!empty($oldValues['watcher'])) {
            $oldValues['watchers'] = array_column($oldValues['watcher'], 'user_id');
            unset($oldValues['watcher']);
        }

        // 计算变化字段
        $changedFields = [];
        foreach ($syncValues as $k => $v) {
            if ($this->compareValue($oldValues[$k] ?? null, $v, $k)) {
                $changedFields[] = $k;
            }
        }

        Log::get()->info('同步问题变更,存在变化的字段有：', [
            'issue_id' => $issueId,
            'changed_fields' => $changedFields,
            'old_values' => $oldValues,
            'new_values' => $syncValues
        ]);


        if (!empty(array_filter($changedFields, function ($item) {
            return $item == 'description';
        }))) {

        }


        // 调用同步方法
        $this->syncIssueChanges($issueId, $changedFields, $syncValues, $extValues);

        return $syncValues;
    }

    /**
     * 比较新旧值是否不同
     */
    protected function compareValue($oldValue, $newValue, string $key): bool
    {
        // 附件特殊处理：比对 id 集合
        if ($key === 'attachment') {
            $oldIds = array_column($oldValue ?? [], 'id');
            $newIds = array_column($newValue ?? [], 'id');
            sort($oldIds);
            sort($newIds);
            return $oldIds !== $newIds;
        }

        // watchers、assigned：比对 user_id 数组
        if (in_array($key, ['watchers', 'assigned'], true)) {
            $oldArr = array_map('intval', $oldValue ?? []);
            $newArr = array_map('intval', $newValue ?? []);
            sort($oldArr);
            sort($newArr);
            return $oldArr !== $newArr;
        }

        // 其他字段：直接对比
        return $oldValue !== $newValue;
    }


    /**
     * 执行同步操作
     *
     * @param int $targetIssueId 目标问题ID
     * @param array $fieldsToSync 需要同步的字段
     * @param array $values 新值
     * @param int $relationId 同步关系ID
     * @param int $userId 操作用户ID
     * @param array $extValues 扩展值
     * @return void
     */
    protected function performSync(int $targetIssueId, array $fieldsToSync, array $values, int $relationId, int $userId, array $extValues): void
    {
        try {
            $targetIssue = make(IssueModel::class)->find($targetIssueId);

            if (!$targetIssue) {
                Log::get('debug')->error('目标问题不存在', ['issue_id' => $targetIssueId]);
                return;
            }

            Db::connection('tchip_redmine')->beginTransaction();

            try {
                $currentUserId = $userId;

                // 创建日志记录
                $journal = new JournalsModel();
                $journal->journalized_type = 'Issue';
                $journal->journalized_id = $targetIssueId;
                $journal->user_id = $currentUserId;
                $journal->notes = "通过同步关系自动更新";
                $journal->created_on = Carbon::now();
                $journal->private_notes = 0;
                $journal->save();

                // 定义需要特殊处理的字段
                $specialFields = ['description', 'attachment', 'watchers', 'assigned'];

                // 分离普通字段和特殊字段
                $normalFields = array_diff($fieldsToSync, $specialFields);
                $specialFieldsToSync = array_intersect($fieldsToSync, $specialFields);

                // 处理普通字段
                foreach ($normalFields as $field) {
                    if (isset($values[$field])) {
                        $this->syncNormalField($targetIssue, $field, $values[$field], $journal->id, $relationId);
                    }
                }

                // 处理特殊字段
                foreach ($specialFieldsToSync as $field) {
                    if (isset($values[$field])) {
                        $this->syncSpecialField($targetIssue, $field, $values[$field], $journal->id, $relationId, $extValues);
                    }
                }

                // 更新问题的修改时间
                $targetIssue->updated_on = Carbon::now();
                $targetIssue->save();

                Db::connection('tchip_redmine')->commit();

                 Log::get()->info('同步操作成功', [
                    'target_issue_id' => $targetIssueId,
                    'synced_fields' => $fieldsToSync,
                    'relation_id' => $relationId
                ]);

            } catch (\Exception $e) {
                Db::connection('tchip_redmine')->rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::get('debug')->error('执行同步操作失败', [
                'target_issue_id' => $targetIssueId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 同步普通字段
     *
     * @param IssueModel $targetIssue 目标问题
     * @param string $field 字段名
     * @param mixed $newValue 新值
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @return void
     */
    protected function syncNormalField(IssueModel $targetIssue, string $field, $newValue, int $journalId, int $relationId): void
    {
        $oldValue = $targetIssue->{$field};

        // 更新问题字段
        $targetIssue->{$field} = $newValue;

        // 记录变更详情
        $journalDetail = new JournalDetailsModel();
        $journalDetail->journal_id = $journalId;
        $journalDetail->property = 'attr';
        $journalDetail->prop_key = $field;
        $journalDetail->old_value = $oldValue;
        $journalDetail->value = $newValue;
        $journalDetail->sync_type = JournalDetailsModel::SYNC_TYPE_SYNC;
        $journalDetail->relation_id = $relationId;
        $journalDetail->save();
    }

    /**
     * 同步特殊字段
     *
     * @param IssueModel $targetIssue 目标问题
     * @param string $field 字段名
     * @param mixed $newValue 新值
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @param array $extValues 扩展值
     * @return void
     */
    protected function syncSpecialField(IssueModel $targetIssue, string $field, $newValue, int $journalId, int $relationId, array $extValues): void
    {
        switch ($field) {
            case 'description':
                $this->syncDescriptionField($targetIssue, $newValue, $journalId, $relationId, $extValues);
                break;
            case 'attachment':
                $this->syncAttachmentField($targetIssue, $newValue, $journalId, $relationId);
                break;
            case 'watchers':
                $this->syncWatchersField($targetIssue, $newValue, $journalId, $relationId);
                break;
            case 'assigned':
                $this->syncAssignedField($targetIssue, $newValue, $journalId, $relationId);
                break;
            default:
                // 如果不是特殊字段，使用普通字段处理方式
                $this->syncNormalField($targetIssue, $field, $newValue, $journalId, $relationId);
                break;
        }
    }

    /**
     * 同步描述字段
     * 需要同时处理 description 和 description_html
     *
     * @param IssueModel $targetIssue 目标问题
     * @param mixed $newValue 新值
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @param array $extValues 扩展值
     * @return void
     */
    protected function syncDescriptionField(IssueModel $targetIssue, $newValue, int $journalId, int $relationId, array $extValues): void
    {
        $oldValue = $targetIssue->description;

        // 更新描述字段
        $targetIssue->description = $newValue;

        // 同时更新description_html字段
        $targetIssueExt = make(IssuesExtModel::class)::query()->where('issue_id', $targetIssue->id)->first();
        $targetIssueExt->description_html = $extValues['description_html'] ?? '';
        $targetIssueExt->save();

        // 记录变更详情
        $journalDetail = new JournalDetailsModel();
        $journalDetail->journal_id = $journalId;
        $journalDetail->property = 'attr';
        $journalDetail->prop_key = 'description';
        $journalDetail->old_value = $oldValue;
        $journalDetail->value = $newValue;
        $journalDetail->sync_type = JournalDetailsModel::SYNC_TYPE_SYNC;
        $journalDetail->relation_id = $relationId;
        $journalDetail->save();

        Log::get()->info('同步描述字段成功', [
            'target_issue_id' => $targetIssue->id,
            'relation_id' => $relationId
        ]);
    }

    /**
     * 同步附件字段
     * 处理附件数组类型数据
     *
     * @param IssueModel $targetIssue 目标问题
     * @param array $newValue 新值（附件数组）
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @return void
     */
    protected function syncAttachmentField(IssueModel $targetIssue, array $newValue, int $journalId, int $relationId): void
    {
        // 获取当前附件
        $currentAttachments = $targetIssue->attachment ?? [];
        $oldAttachmentIds = array_column($currentAttachments, 'id');
        $newAttachmentIds = array_column($newValue, 'id');

        // 记录变更详情
        $journalDetail = new JournalDetailsModel();
        $journalDetail->journal_id = $journalId;
        $journalDetail->property = 'attr';
        $journalDetail->prop_key = 'attachment';
        $journalDetail->old_value = json_encode($oldAttachmentIds);
        $journalDetail->value = json_encode($newAttachmentIds);
        $journalDetail->sync_type = JournalDetailsModel::SYNC_TYPE_SYNC;
        $journalDetail->relation_id = $relationId;
        $journalDetail->save();

        // TODO: 这里需要根据具体的附件处理逻辑来实现
        // 可能需要调用附件服务来处理附件的添加、删除等操作

        Log::get()->info('同步附件字段成功', [
            'target_issue_id' => $targetIssue->id,
            'old_attachments' => count($oldAttachmentIds),
            'new_attachments' => count($newAttachmentIds),
            'relation_id' => $relationId
        ]);
    }

    /**
     * 同步关注人字段
     * 处理用户ID数组
     *
     * @param IssueModel $targetIssue 目标问题
     * @param array $newValue 新值（用户ID数组）
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @return void
     */
    protected function syncWatchersField(IssueModel $targetIssue, array $newValue, int $journalId, int $relationId): void
    {
        // 获取当前关注人
        $currentWatchers = $targetIssue->watcher ?? [];
        $oldWatcherIds = array_column($currentWatchers, 'user_id');

        // 过滤并确保新值都是数字ID
        $newWatcherIds = array_filter($newValue, 'is_numeric');
        $newWatcherIds = array_map('intval', $newWatcherIds);

        // 记录变更详情
        $journalDetail = new JournalDetailsModel();
        $journalDetail->journal_id = $journalId;
        $journalDetail->property = 'attr';
        $journalDetail->prop_key = 'watchers';
        $journalDetail->old_value = json_encode($oldWatcherIds);
        $journalDetail->value = json_encode($newWatcherIds);
        $journalDetail->sync_type = JournalDetailsModel::SYNC_TYPE_SYNC;
        $journalDetail->relation_id = $relationId;
        $journalDetail->save();

        // TODO: 这里需要根据具体的关注人处理逻辑来实现
        // 可能需要调用用户服务来处理关注人的添加、删除等操作

        Log::get()->info('同步关注人字段成功', [
            'target_issue_id' => $targetIssue->id,
            'old_watchers' => count($oldWatcherIds),
            'new_watchers' => count($newWatcherIds),
            'relation_id' => $relationId
        ]);
    }

    /**
     * 同步处理人字段
     * 处理用户ID数组
     *
     * @param IssueModel $targetIssue 目标问题
     * @param array $newValue 新值（用户ID数组）
     * @param int $journalId 日志ID
     * @param int $relationId 同步关系ID
     * @return void
     */
    protected function syncAssignedField(IssueModel $targetIssue, array $newValue, int $journalId, int $relationId): void
    {
        // 获取当前处理人
        $currentAssigned = $targetIssue->issueAssigned ?? [];
        $oldAssignedIds = array_column($currentAssigned, 'user_id');

        // 过滤并确保新值都是数字ID
        $newAssignedIds = array_filter($newValue, 'is_numeric');
        $newAssignedIds = array_map('intval', $newAssignedIds);

        // 记录变更详情
        $journalDetail = new JournalDetailsModel();
        $journalDetail->journal_id = $journalId;
        $journalDetail->property = 'attr';
        $journalDetail->prop_key = 'assigned';
        $journalDetail->old_value = json_encode($oldAssignedIds);
        $journalDetail->value = json_encode($newAssignedIds);
        $journalDetail->sync_type = JournalDetailsModel::SYNC_TYPE_SYNC;
        $journalDetail->relation_id = $relationId;
        $journalDetail->save();

        // TODO: 这里需要根据具体的处理人处理逻辑来实现
        // 可能需要调用用户服务来处理处理人的添加、删除等操作

        Log::get()->info('同步处理人字段成功', [
            'target_issue_id' => $targetIssue->id,
            'old_assigned' => count($oldAssignedIds),
            'new_assigned' => count($newAssignedIds),
            'relation_id' => $relationId
        ]);
    }

    /**
     * 查找活跃的同步关系
     *
     * @param int $issueId
     * @return Collection
     */
    protected function findActiveSyncRelations(int $issueId): Collection
    {
        return $this->relationModel
            ->where('is_active', 1)
            ->whereNull('deleted_at')
            ->where(function ($query) use ($issueId) {
                $query->where('source_issue_id', $issueId)
                    ->orWhere('target_issue_id', $issueId);
            })
            ->get();
    }

    /**
     * 获取同步关系的字段配置
     *
     * @param int $relationId
     * @return array
     */
    protected function getSyncFieldsForRelation(int $relationId): array
    {
        $fields = $this->syncFieldsModel
            ->where('relation_id', $relationId)
            ->where('is_enabled', 1)
            ->orderBy('sort_order', 'asc')
            ->pluck('field_key')
            ->toArray();
            
        // 如果没有特定配置，使用全局配置
        if (empty($fields)) {
            $fields = $this->syncFieldsModel
                ->whereNull('relation_id')
                ->where('is_enabled', 1)
                ->orderBy('sort_order', 'asc')
                ->pluck('field_key')
                ->toArray();
        }
        
        // 如果还是没有，使用默认配置
        if (empty($fields)) {
            $fields = array_keys(array_filter(IssueSyncFieldsModel::$defaultSyncFields, function ($config) {
                return $config['enabled'] ?? false;
            }));
        }
        
        return $fields;
    }

    /**
     * 创建同步字段配置
     *
     * @param int $relationId
     * @param array $fields
     * @return void
     */
    protected function createSyncFieldsConfig(int $relationId, array $fields): void
    {
        foreach ($fields as $index => $field) {
            $syncField = new IssueSyncFieldsModel();
            $syncField->relation_id = $relationId;
            
            if (is_array($field)) {
                $syncField->field_key = $field['key'];
                $syncField->field_name = $field['name'] ?? $field['key'];
                $syncField->is_enabled = $field['enabled'] ?? 1;
                $syncField->sort_order = $field['order'] ?? ($index + 1) * 10;
                $syncField->field_config = $field['config'] ?? null;
                $syncField->description = $field['description'] ?? null;
            } else {
                $syncField->field_key = $field;
                $syncField->field_name = IssueSyncFieldsModel::$defaultSyncFields[$field]['name'] ?? $field;
                $syncField->is_enabled = 1;
                $syncField->sort_order = ($index + 1) * 10;
            }
            
            $syncField->created_at = Carbon::now();
            $syncField->updated_at = Carbon::now();
            $syncField->save();
        }
    }

    /**
     * 创建默认同步字段配置
     *
     * @param int $relationId
     * @return void
     */
    protected function createDefaultSyncFieldsConfig(int $relationId): void
    {
        foreach (IssueSyncFieldsModel::$defaultSyncFields as $key => $config) {
            if ($config['enabled']) {
                $syncField = new IssueSyncFieldsModel();
                $syncField->relation_id = $relationId;
                $syncField->field_key = $key;
                $syncField->field_name = $config['name'];
                $syncField->is_enabled = 1;
                $syncField->sort_order = $config['order'];
                $syncField->created_at = Carbon::now();
                $syncField->updated_at = Carbon::now();
                $syncField->save();
            }
        }
    }

    /**
     * 更新同步字段配置
     *
     * @param int $relationId
     * @param array $fields
     * @return void
     */
    protected function updateSyncFieldsConfig(int $relationId, array $fields): void
    {
        // 删除现有配置
        $this->syncFieldsModel->where('relation_id', $relationId)->delete();
        
        // 创建新配置
        $this->createSyncFieldsConfig($relationId, $fields);
    }

    /**
     * 获取同步关系列表
     *
     * @param array $filter 过滤条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getSyncRelationList(array $filter = [], int $page = 1, int $limit = 20): array
    {
        try {
            $query = $this->relationModel->query()
                ->whereNull('deleted_at')
                ->with(['sourceIssue', 'targetIssue', 'syncFields']);
            
            // 应用过滤条件
            if (!empty($filter['source_issue_id'])) {
                $query->where('source_issue_id', $filter['source_issue_id']);
            }
            
            if (!empty($filter['target_issue_id'])) {
                $query->where('target_issue_id', $filter['target_issue_id']);
            }
            
            if (isset($filter['is_active'])) {
                $query->where('is_active', $filter['is_active']);
            }
            
            if (!empty($filter['sync_direction'])) {
                $query->where('sync_direction', $filter['sync_direction']);
            }
            
            // 分页
            $total = $query->count();
            $offset = ($page - 1) * $limit;
            
            $list = $query
                ->orderBy('id', 'desc')
                ->offset($offset)
                ->limit($limit)
                ->get();
            
            return [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ];
            
        } catch (\Exception $e) {
            Log::get('debug')->error('获取同步关系列表失败', ['error' => $e->getMessage()]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 获取同步关系详情
     *
     * @param int $relationId
     * @return IssueCopyRelationsModel
     * @throws AppException
     */
    public function getSyncRelationDetail(int $relationId): IssueCopyRelationsModel
    {
        try {
            $relation = $this->relationModel
                ->with(['sourceIssue', 'targetIssue', 'syncFields'])
                ->find($relationId);
            
            if (!$relation) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系不存在");
            }
            
            return $relation;
            
        } catch (\Exception $e) {
            Log::get('debug')->error('获取同步关系详情失败', [
                'relation_id' => $relationId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 手动触发同步
     *
     * @param int $relationId 同步关系ID
     * @param string $direction 同步方向 (source_to_target 或 target_to_source)
     * @return bool
     * @throws AppException
     */
    public function triggerManualSync(int $relationId, string $direction = 'source_to_target'): bool
    {
        try {
            $relation = $this->relationModel->find($relationId);
            
            if (!$relation) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系不存在");
            }
            
            if (!$relation->is_active) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系未激活");
            }
            
            // 获取同步字段
            $syncFields = $this->getSyncFieldsForRelation($relationId);
            
            if (empty($syncFields)) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "没有配置同步字段");
            }
            
            // 根据方向确定源和目标
            if ($direction === 'source_to_target') {
                $sourceIssue = make(IssueModel::class)->find($relation->source_issue_id);
                $targetIssueId = $relation->target_issue_id;
            } else {
                $sourceIssue = make(IssueModel::class)->find($relation->target_issue_id);
                $targetIssueId = $relation->source_issue_id;
            }
            
            if (!$sourceIssue) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "源问题不存在");
            }
            
            // 准备同步数据
            $values = [];
            foreach ($syncFields as $field) {
                $values[$field] = $sourceIssue->{$field};
            }
            
            // 执行同步
            $this->performSync($targetIssueId, $syncFields, $values, $relationId);
            
             Log::get()->info('手动触发同步成功', [
                'relation_id' => $relationId,
                'direction' => $direction
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::get('debug')->error('手动触发同步失败', [
                'relation_id' => $relationId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 获取当前用户
     *
     * @return int|null
     */
    protected function getCurrentUser(): ?int
    {
        try {
            return getRedmineUserId();
        } catch (\Exception $e) {
            Log::get('debug')->error('获取当前用户失败', ['error' => $e->getMessage()]);
        }
        
        return null;
    }

    /**
     * 批量创建同步关系
     *
     * @param array $relations 同步关系数组
     * @return array
     */
    public function batchCreateSyncRelations(array $relations): array
    {
        $results = [
            'success' => [],
            'failed' => []
        ];
        
        foreach ($relations as $relation) {
            try {
                $syncRelation = $this->createSyncRelation(
                    $relation['source_issue_id'],
                    $relation['target_issue_id'],
                    $relation['sync_direction'] ?? IssueCopyRelationsModel::SYNC_BIDIRECTIONAL,
                    $relation['sync_fields'] ?? []
                );
                
                $results['success'][] = [
                    'relation_id' => $syncRelation->id,
                    'source_issue_id' => $relation['source_issue_id'],
                    'target_issue_id' => $relation['target_issue_id']
                ];
                
            } catch (\Exception $e) {
                $results['failed'][] = [
                    'source_issue_id' => $relation['source_issue_id'],
                    'target_issue_id' => $relation['target_issue_id'],
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }

    /**
     * 检查同步冲突
     *
     * @param int $relationId
     * @return array
     */
    public function checkSyncConflicts(int $relationId): array
    {
        try {
            $relation = $this->relationModel->find($relationId);
            
            if (!$relation) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "同步关系不存在");
            }
            
            $sourceIssue = make(IssueModel::class)->find($relation->source_issue_id);
            $targetIssue = make(IssueModel::class)->find($relation->target_issue_id);
            
            if (!$sourceIssue || !$targetIssue) {
                throw new AppException(StatusCode::ERR_EXCEPTION, "问题不存在");
            }
            
            $syncFields = $this->getSyncFieldsForRelation($relationId);
            $conflicts = [];
            
            foreach ($syncFields as $field) {
                if ($sourceIssue->{$field} != $targetIssue->{$field}) {
                    $conflicts[] = [
                        'field' => $field,
                        'source_value' => $sourceIssue->{$field},
                        'target_value' => $targetIssue->{$field}
                    ];
                }
            }
            
            return [
                'has_conflicts' => !empty($conflicts),
                'conflicts' => $conflicts
            ];
            
        } catch (\Exception $e) {
            Log::get('debug')->error('检查同步冲突失败', [
                'relation_id' => $relationId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
}
