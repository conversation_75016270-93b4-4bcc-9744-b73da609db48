<?php

declare (strict_types=1);

namespace App\Model\Redmine;

use Carbon\Carbon;
use Cassandra\Date;
use Hyperf\Crontab\Annotation\Crontab;
use Parsedown;
use League\HTMLToMarkdown\HtmlConverter;
use Hyperf\Cache\Annotation\Cacheable;
/**
 * @property int $id
 * @property int $tracker_id
 * @property int $project_id
 * @property string $subject
 * @property string $description
 * @property string $due_date
 * @property int $category_id
 * @property int $status_id
 * @property int $assigned_to_id
 * @property int $priority_id
 * @property int $fixed_version_id
 * @property int $author_id
 * @property int $lock_version
 * @property string $created_on
 * @property string $updated_on
 * @property string $start_date
 * @property int $done_ratio
 * @property string $estimated_hours
 * @property int $parent_id
 * @property int $root_id
 * @property int $lft
 * @property int $rgt
 * @property int $is_private
 * @property string $closed_on
 * @property string $class_id
 * @property string $class_pid
 */
class IssueModel extends RedmineBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'issues';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['tracker_id', 'project_id', 'subject', 'description',
        'due_date', 'category_id', 'status_id', 'assigned_to_id',
        'priority_id', 'author_id', 'lock_version', 'created_on',
        'fixed_version_id', 'parent_id', 'root_id', 'class_id', 'class_pid','lft', 'rgt',
        'updated_on', 'start_date', 'done_ratio', 'estimated_hours', 'is_private', 'closed_on'
    ];
    
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id'          => 'integer', 'tracker_id' => 'integer',
        'project_id'  => 'integer', 'category_id' => 'integer',
        'status_id'   => 'integer', 'assigned_to_id' => 'integer',
        'priority_id' => 'integer', 'fixed_version_id' => 'integer',
        'author_id'   => 'integer', 'lock_version' => 'integer',
        'done_ratio'  => 'integer', 'parent_id' => 'integer',
        'root_id'     => 'integer', 'lft' => 'integer',
        'rgt'         => 'integer', 'is_private' => 'integer',
        'updated_on'  => 'datetime:Y-m-d H:i'
    ];

    public $fieldName = [
        'tracker_id'       => '跟踪',
        'project_id'       => '项目',
        'subject'          => '标题',
        'description'      => '内容',
        'due_date'         => '',
        'category_id'      => '分类',
        'status_id'        => '状态',
        'assigned_to_id'   => '指派给',
        'priority_id'      => '优化级',
        'fixed_version_id' => '',
        'author_id'        => '创建人',
        'lock_version'     => '',
        'created_on'       => '创建于',
        'updated_on'       => '更新于',
        'start_date'       => '开始于',
        'done_ratio'       => '完成进度',
        'estimated_hours'  => '预计用时',
        'parent_id'        => '上级任务',
        'root_id'          => '',
        'lft'              => '',
        'rgt'              => '',
        'is_private'       => '保护',
        'closed_on'        => '关闭于'
    ];

    protected $appends = ['created_on_text', 'priority_text', 'description_html', 'project_type'];

    public $status_close = [3,4,5,13];

    public $status_todo = [1,2,6,7,9,10,11,12];
    public $statusTodoProject = [1,2,6,7,9,10];


    public function childIssue()
    {
        return $this->hasMany('\App\Model\Redmine\IssueModel', 'parent_id', 'id');
    }

    public function issueType()
    {
        return $this->hasOne('\App\Model\Redmine\TrackersModel', 'id', 'tracker_id');
    }

    public function attachment()
    {
        return $this->hasMany('\App\Model\Redmine\AttachmentModel', 'container_id', 'id')->where('container_type', 'Issue');
    }

    public function issueExt()
    {
        return $this->hasOne('\App\Model\Redmine\IssuesExtModel', 'issue_id', 'id');
    }

    public function issueAssigned()
    {
        return $this->hasMany('\App\Model\Redmine\IssueAssignedModel', 'issue_id', 'id');
    }

    public function issueStatus()
    {
        return $this->belongsTo('\App\Model\Redmine\IssueStatusModel', 'status_id', 'id');
    }

    public function enumeration()
    {
        return $this->belongsTo('\App\Model\Redmine\EnumerationModel', 'priority_id', 'id');
    }

    /**
     * 负责人关联
     * @return \Hyperf\Database\Model\Relations\BelongsTo
     */
    public function assignedText()
    {
        return $this->belongsTo('\App\Model\Redmine\UserModel', 'assigned_to_id', 'id');
    }

    public function projectText()
    {
        return $this->belongsTo('\App\Model\Redmine\ProjectModel', 'project_id', 'id');
    }

    public function project()
    {
        return $this->belongsTo('\App\Model\Redmine\ProjectModel', 'project_id', 'id');
    }

    public function projectExt()
    {
        return $this->belongsTo('\App\Model\Redmine\ProjectsExtModel', 'project_id', 'project_id');
    }

    public function authorText()
    {
        return $this->belongsTo('\App\Model\Redmine\UserModel', 'author_id', 'id');
    }

    public function versionText()
    {
        return $this->belongsTo('\App\Model\Redmine\VersionModel', 'fixed_version_id', 'id');
    }

    public function watcher()
    {
        return $this->hasMany('\App\Model\Redmine\WatchersModel', 'watchable_id', 'id');
    }

    public function customValues()
    {
        return $this->hasMany('\App\Model\Redmine\CustomValuesModel', 'customized_id', 'id');
    }

    public function customFields()
    {
        return $this->hasMany('\App\Model\Redmine\CustomValuesModel', 'customized_id', 'id');
    }

    public function relations()
    {

    }

    public function enumerations()
    {
        return $this->hasMany('\App\Model\Redmine\EnumerationModel', 'id', 'priority_id');
    }

    public function customFieldsMultiAssignId()
    {
        return $this->hasMany('\App\Model\Redmine\CustomValuesModel', 'customized_id', 'id');
    }

    public function tracker()
    {
        return $this->belongsTo('\App\Model\Redmine\TrackersModel', 'tracker_id', 'id');
    }

    public function categoryText()
    {
        return $this->hasOne(IssueCategoriesModel::class, 'id','category_id');
    }

    /**
     * 关联检查清单
     */
    public function checklists()
    {
        return $this->hasMany('\App\Model\Redmine\CheckList\ChecklistModel', 'issue_id', 'id')
            ->whereNull('deleted_at')
            ->orderBy('created_at', 'ASC');
    }

    public function getProjectTypeAttribute()
    {
        $projectType = '';
        if (isset($this->attributes['project_id']) && isset($this->attributes['subject'])) {
            $projectType = $this->getProjectTypeAttributeCache($this->attributes['project_id']);
        }

        return $projectType;
    }

    /**
     * @Cacheable (prefix="project_type_attribute_cache_", ttl=10)
     */
    public function getProjectTypeAttributeCache($project_id)
    {
        return ProjectsExtModel::query()->where('project_id', $project_id)->value('project_type');
    }


    public function getUpdatedOnAttribute()
    {
        $dt = new \DateTime($this->attributes['updated_on'],new \DateTimeZone('UTC'));
        $dt->setTimezone(new \DateTimeZone('Asia/Shanghai'));
        return $dt->format('Y-m-d H:i');
    }

    public function getPriorityTextAttribute()
    {
        $value = '';
        if (isset($this->attributes['priority_id'])) {
//            $value = EnumerationModel::query()->where('id', $this->attributes['priority_id'])->value('name');
            $value = $this->priorityTextAttributeCache($this->attributes['priority_id']) ?? '';
        }
        return $value;
    }

    /**
     * @Cacheable (prefix="priority_", ttl=10)
     */
    public function priorityTextAttributeCache($id)
    {
        return EnumerationModel::query()->where('id', $id)->value('name');
    }

    public function getCreatedOnAttribute()
    {
        if (!empty($this->attributes['created_on'])) {
            $dt = new \DateTime($this->attributes['created_on'],new \DateTimeZone('UTC'));
            $dt->setTimezone(new \DateTimeZone('Asia/Shanghai'));
            return $dt->format('Y-m-d H:i');
        }
    }

    public function getCreatedOnTextAttribute()
    {
        if (!empty($this->attributes['created_on'])) {
            Carbon::setLocale('zh');
            return Carbon::createFromFormat('Y-m-d H:i:s', $this->attributes['created_on'], 'UTC')
                ->diffForHumans(Carbon::now('Asia/Shanghai'));
        }
    }

    public function getDescriptionHtmlAttribute()
    {
        $value = '';
        if (!empty($this->attributes['description'])) {
            $value = $this->formatIssueDesc2($this->attributes['id'] ?? null, $this->attributes['description']);
        }
        return $value;
    }

    public function getDescriptionAttribute()
    {
        $value = '';
        if (!empty($this->attributes['description'])) {
            if (hasHtmlTags($this->attributes['description']) && !isMarkdown($this->attributes['description'])) {
                $converter = make(HtmlConverter::class);
                $value =  $converter->convert($this->attributes['description']);
            } else {
                $value = $this->attributes['description'];
            }
            $value = $value ? handleMarkdownImg($value, $this->attributes['id'] ?? '') : $value;
        }
        return $value;
    }

    /**
     * 格式化任务描述
     * @param $id
     * @param $description
     * @return array|string|string[]
     */
    public function formatIssueDesc($id, $description)
    {
        $description = Parsedown::instance()->text($description);
        // 获取img标签
        preg_match_all('/<img.*?(?:>|\/>)/', $description, $images);
        if (!empty($images[0]) && count($images[0]) > 0) {
            foreach ($images[0] as $ival) {
                // 获取url
                preg_match('/src=[\'\"]?([^\'\"]*)[\'\"]?/', $ival, $img);
                if (!empty($img[1]) && strpos($img[1], 'http') !== 0 && $id) {
                    $attUrl         = AttachmentModel::query()->where(['container_id' => $id, 'container_type' => 'Issue', 'filename' => $img[1]])->first();
                    $redmineFileUrl = !empty($attUrl->url) ? $attUrl->url : $img[1];
                    $description    = str_replace($img[1], $redmineFileUrl, $description);
                }
            }
        }
        return $description;
    }

    /**
     * html化任务描述, 去除格式化完成后遗留的 \n 标签（其会在前端展示造成多余换行）
     * @param $id
     * @param $description
     * @return array|string|string[]
     */
    public function formatIssueDesc2($id, $description)
    {
        // 1. Markdown → HTML
        $parsedown = Parsedown::instance();
        $parsedown->setBreaksEnabled(true);
        $html = $parsedown->text($description);

        // 2. 替换图片 src
        $html = preg_replace_callback(
            '/<img\b([^>]*?)src=[\'\"]?([^\'\">\s]+)[\'\"]?([^>]*?)>/i',
            function ($m) use ($id) {
                $src = $m[2];
                if ($id && !empty($src) && strpos($src, 'http') !== 0) {
                    $att = AttachmentModel::query()
                        ->where([
                            'container_id'   => $id,
                            'container_type' => 'Issue',
                            'filename'       => $src,
                        ])
                        ->first();
                    if ($att && $att->url) {
                        $src = $att->url;
                    }
                }
                return '<img' . $m[1] . 'src="' . $src . '"' . $m[3] . '>';
            },
            $html
        );

        // 3. 保护 <pre> 和 <code> 区块
        $placeholders = [];
        $protect = function ($matches) use (&$placeholders) {
            $key = '%%'.uniqid().'%%';
            $placeholders[$key] = $matches[0];
            return $key;
        };
        // 把 <pre>...</pre> 和 <code>...</code> 先替换成占位符
        $html = preg_replace_callback('#<pre\b[^>]*>.*?</pre>#is', $protect, $html);
        $html = preg_replace_callback('#<code\b[^>]*>.*?</code>#is', $protect, $html);

        // 4. 在剩余文本里删除 \r \n
        $html = str_replace(["\r", "\n"], '', $html);

        // 5. 把占位符还原成原始区块
        $html = str_replace(array_keys($placeholders), array_values($placeholders), $html);

        return $html;
    }
}
