<?php

declare(strict_types=1);

namespace App\Model\Redmine;

use Carbon\Carbon;

/**
 * 问题同步关系模型
 * @property int $id
 * @property int $source_issue_id 源问题ID
 * @property int $target_issue_id 目标问题ID
 * @property string $sync_direction 同步方向: source_to_target|target_to_source|bidirectional
 * @property int $is_active 是否激活同步关系
 * @property string $created_by 创建人
 * @property string $updated_by 更新人
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 */
class IssueCopyRelationsModel extends RedmineBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'issue_copy_relations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'source_issue_id',
        'target_issue_id',
        'sync_direction',
        'is_active',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'source_issue_id' => 'integer',
        'target_issue_id' => 'integer',
        'is_active' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'deleted_at' => 'datetime:Y-m-d H:i:s'
    ];

    /**
     * 字段名称映射
     *
     * @var array
     */
    public $fieldName = [
        'source_issue_id' => '源问题ID',
        'target_issue_id' => '目标问题ID',
        'sync_direction' => '同步方向',
        'is_active' => '激活状态',
        'created_by' => '创建人',
        'updated_by' => '更新人',
        'created_at' => '创建时间',
        'updated_at' => '更新时间',
        'deleted_at' => '删除时间'
    ];

    /**
     * 同步方向常量
     */
    const SYNC_SOURCE_TO_TARGET = 'source_to_target';
    const SYNC_TARGET_TO_SOURCE = 'target_to_source';
    const SYNC_BIDIRECTIONAL = 'bidirectional';

    /**
     * 获取源问题关联
     *
     * @return \Hyperf\Database\Model\Relations\BelongsTo
     */
    public function sourceIssue()
    {
        return $this->belongsTo('\App\Model\Redmine\IssueModel', 'source_issue_id', 'id');
    }

    /**
     * 获取目标问题关联
     *
     * @return \Hyperf\Database\Model\Relations\BelongsTo
     */
    public function targetIssue()
    {
        return $this->belongsTo('\App\Model\Redmine\IssueModel', 'target_issue_id', 'id');
    }

    /**
     * 获取同步字段配置关联
     *
     * @return \Hyperf\Database\Model\Relations\HasMany
     */
    public function syncFields()
    {
        return $this->hasMany('\App\Model\Redmine\IssueSyncFieldsModel', 'relation_id', 'id');
    }

    /**
     * 获取创建人关联
     *
     * @return \Hyperf\Database\Model\Relations\BelongsTo
     */
    public function createdBy()
    {
        return $this->belongsTo('\App\Model\Redmine\UserModel', 'created_by', 'login');
    }

    /**
     * 获取更新人关联
     *
     * @return \Hyperf\Database\Model\Relations\BelongsTo
     */
    public function updatedBy()
    {
        return $this->belongsTo('\App\Model\Redmine\UserModel', 'updated_by', 'login');
    }

    /**
     * 判断是否为双向同步
     *
     * @return bool
     */
    public function isBidirectional()
    {
        return $this->sync_direction === self::SYNC_BIDIRECTIONAL;
    }

    /**
     * 判断是否可以从源同步到目标
     *
     * @return bool
     */
    public function canSyncFromSource()
    {
        return in_array($this->sync_direction, [self::SYNC_SOURCE_TO_TARGET, self::SYNC_BIDIRECTIONAL]);
    }

    /**
     * 判断是否可以从目标同步到源
     *
     * @return bool
     */
    public function canSyncFromTarget()
    {
        return in_array($this->sync_direction, [self::SYNC_TARGET_TO_SOURCE, self::SYNC_BIDIRECTIONAL]);
    }

    /**
     * 重写时间字段常量
     */
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const DELETED_AT = 'deleted_at';
}
