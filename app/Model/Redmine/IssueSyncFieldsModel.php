<?php

declare(strict_types=1);

namespace App\Model\Redmine;

use Carbon\Carbon;

/**
 * 问题同步字段配置模型
 * @property int $id
 * @property int $relation_id 关联的同步关系ID，null表示全局配置
 * @property string $field_key 需要同步的字段键名
 * @property string $field_name 字段显示名称
 * @property int $is_enabled 是否启用该字段同步
 * @property int $sort_order 排序顺序
 * @property array $field_config 字段额外配置JSON
 * @property string $description 字段说明
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class IssueSyncFieldsModel extends RedmineBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'issue_sync_fields';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'relation_id',
        'field_key',
        'field_name',
        'is_enabled',
        'sort_order',
        'field_config',
        'description',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'relation_id' => 'integer',
        'is_enabled' => 'integer',
        'sort_order' => 'integer',
        'field_config' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s'
    ];

    /**
     * 字段名称映射
     *
     * @var array
     */
    public $fieldName = [
        'relation_id' => '关系ID',
        'field_key' => '字段键名',
        'field_name' => '字段名称',
        'is_enabled' => '启用状态',
        'sort_order' => '排序',
        'field_config' => '字段配置',
        'description' => '说明',
        'created_at' => '创建时间',
        'updated_at' => '更新时间'
    ];

    /**
     * 常用同步字段定义
     */
    const FIELD_SUBJECT = 'subject';
    const FIELD_DESCRIPTION = 'description';
    const FIELD_STATUS = 'status_id';
    const FIELD_PRIORITY = 'priority_id';
    const FIELD_ASSIGNED_TO = 'assigned_to_id';
    const FIELD_CATEGORY = 'category_id';
    const FIELD_FIXED_VERSION = 'fixed_version_id';
    const FIELD_START_DATE = 'start_date';
    const FIELD_DUE_DATE = 'due_date';
    const FIELD_DONE_RATIO = 'done_ratio';
    const FIELD_ESTIMATED_HOURS = 'estimated_hours';
    const FIELD_TRACKER = 'tracker_id';
    const FIELD_PARENT = 'parent_id';
    const FIELD_IS_PRIVATE = 'is_private';

    /**
     * 默认同步字段配置
     * 
     * @var array
     */
    public static $defaultSyncFields = [
        self::FIELD_SUBJECT => ['name' => '标题', 'enabled' => true, 'order' => 10],
        self::FIELD_DESCRIPTION => ['name' => '描述', 'enabled' => true, 'order' => 20],
        self::FIELD_STATUS => ['name' => '状态', 'enabled' => true, 'order' => 30],
        self::FIELD_PRIORITY => ['name' => '优先级', 'enabled' => true, 'order' => 40],
        self::FIELD_ASSIGNED_TO => ['name' => '指派给', 'enabled' => true, 'order' => 50],
        self::FIELD_CATEGORY => ['name' => '分类', 'enabled' => false, 'order' => 60],
        self::FIELD_FIXED_VERSION => ['name' => '目标版本', 'enabled' => false, 'order' => 70],
        self::FIELD_START_DATE => ['name' => '开始日期', 'enabled' => false, 'order' => 80],
        self::FIELD_DUE_DATE => ['name' => '计划完成日期', 'enabled' => false, 'order' => 90],
        self::FIELD_DONE_RATIO => ['name' => '完成进度', 'enabled' => true, 'order' => 100],
        self::FIELD_ESTIMATED_HOURS => ['name' => '预计工时', 'enabled' => false, 'order' => 110],
    ];

    /**
     * 获取关联的同步关系
     *
     * @return \Hyperf\Database\Model\Relations\BelongsTo
     */
    public function relation()
    {
        return $this->belongsTo('\App\Model\Redmine\IssueCopyRelationsModel', 'relation_id', 'id');
    }

    /**
     * 判断是否为全局配置
     *
     * @return bool
     */
    public function isGlobalConfig()
    {
        return is_null($this->relation_id);
    }

    /**
     * 获取特定关系的同步字段配置
     *
     * @param int|null $relationId
     * @return \Hyperf\Database\Model\Collection
     */
    public static function getSyncFieldsByRelation($relationId = null)
    {
        return self::query()
            ->where('relation_id', $relationId)
            ->where('is_enabled', 1)
            ->orderBy('sort_order', 'asc')
            ->get();
    }

    /**
     * 获取字段配置
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getConfig($key, $default = null)
    {
        if (!is_array($this->field_config)) {
            return $default;
        }
        
        return $this->field_config[$key] ?? $default;
    }

    /**
     * 设置字段配置
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function setConfig($key, $value)
    {
        $config = $this->field_config ?? [];
        $config[$key] = $value;
        $this->field_config = $config;
    }

    /**
     * 重写时间字段常量
     */
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const DELETED_AT = null;
}
